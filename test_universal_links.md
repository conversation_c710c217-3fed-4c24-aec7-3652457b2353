# Universal Links Testing Guide

## Current Issues Fixed:
1. ✅ Added missing associated domains to all entitlement files
2. ✅ Fixed duplicate keys in Runner.entitlements
3. ✅ Updated app group names to: group.com.flowkar.apps & group.com.flowkar.apps.onesignal
4. ✅ Added production environment configuration

## Critical Issue - Server Configuration:
❌ **Your server's apple-app-site-association file has the wrong App ID**

### Current server file:
```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appID": "784953ZVC9.com.flowkar.app",
        "paths": ["*", "/api/*"]
      }
    ]
  }
}
```

### Required server file:
```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appID": "8L4F2RPC2R.com.flowkar.app",
        "paths": ["*", "/api/*"]
      }
    ]
  }
}
```

## Steps to Fix:

### 1. Update Server Configuration
Update the file at `https://api.flowkar.com/.well-known/apple-app-site-association` with the correct App ID: `8L4F2RPC2R.com.flowkar.app`

### 2. Verify Server Configuration
Test the server configuration:
```bash
curl https://api.flowkar.com/.well-known/apple-app-site-association
```

### 3. Test Universal Links
After updating the server:

1. **Build and install the app** on a physical iOS device
2. **Test with Safari**: Open Safari and navigate to `https://api.flowkar.com/api/post-profile-deeplink/123`
3. **Test with Messages**: Send yourself a message with the link
4. **Test with Notes**: Create a note with the link and tap it

### 4. Verify in iOS Settings
Go to Settings > Developer > Universal Links and check if your domain is listed.

### 5. Debug with Console
Use Xcode Console to see deep link logs when testing.

## Expected Behavior:
- ✅ Link should open directly in your app (no Safari popup)
- ✅ No App Store redirect
- ✅ Immediate app launch like Instagram

## Common Issues:
- Server file must be served with `Content-Type: application/json`
- No redirects allowed for the association file
- App must be installed from App Store or TestFlight for production testing
- Development builds may not work with universal links in some cases

## Testing Commands:
```bash
# Test association file
curl -I https://api.flowkar.com/.well-known/apple-app-site-association

# Validate JSON
curl https://api.flowkar.com/.well-known/apple-app-site-association | python -m json.tool
```
