import 'dart:convert';

extension StringUrlValidation on String {
  bool get isUrl => Uri.tryParse(this)?.isAbsolute ?? false;
}

extension StringUtf8Validation on String {
  /// Safely handles UTF-8 strings and removes invalid characters
  String get safeUtf8 {
    try {
      // Convert to UTF-8 bytes and back to ensure proper encoding
      final bytes = utf8.encode(this);
      return utf8.decode(bytes, allowMalformed: false);
    } catch (e) {
      // If encoding fails, remove invalid characters
      return replaceAll(RegExp(r'[^\u0000-\uFFFF]'), '');
    }
  }

  /// Removes replacement characters (�) from the string
  String get removeReplacementChars {
    return replaceAll('\uFFFD', '');
  }

  /// Safely processes text for display, handling UTF-8 issues
  String get safeForDisplay {
    return safeUtf8.removeReplacementChars;
  }
}

typedef StringsCallBack = void Function(String imagepath);
