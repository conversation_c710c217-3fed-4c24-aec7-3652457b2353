import 'package:flowkar/core/utils/exports.dart';

class CaptionPreviewWidget extends StatefulWidget {
  final String caption;
  final String comment;
  final VoidCallback? commentonTap;
  final bool isTextPost;

  const CaptionPreviewWidget({
    super.key,
    required this.caption,
    required this.comment,
    this.commentonTap,
    this.isTextPost = false,
  });

  @override
  State<CaptionPreviewWidget> createState() => _CaptionPreviewWidgetState();
}

class _CaptionPreviewWidgetState extends State<CaptionPreviewWidget> {
  bool _isCaptionExpanded = false;
  bool _hasCaptionOverflow = false;

  bool _isCommentExpanded = false;
  bool _hasCommentOverflow = false;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        //Caption

        final TextStyle? captionStyle = Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 12.5.sp);

        final TextSpan captionTextSpan = TextSpan(
          text: widget.caption.safeForDisplay,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 12.5.sp),
        );

        final TextPainter captionTextPainter = TextPainter(
          text: captionTextSpan,
          maxLines: 2,
          textDirection: TextDirection.ltr,
        )..layout(maxWidth: constraints.maxWidth);

        _hasCaptionOverflow = captionTextPainter.didExceedMaxLines;

        //Comment
        final TextSpan commentTextSpan = TextSpan(
          text: widget.comment,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 12.5.sp),
        );

        final TextPainter commentTextPainter = TextPainter(
          text: commentTextSpan,
          maxLines: 2,
          textDirection: TextDirection.ltr,
        )..layout(maxWidth: constraints.maxWidth);

        _hasCommentOverflow = commentTextPainter.didExceedMaxLines;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Caption display
            if (!widget.isTextPost)
              // if (widget.caption.isNotEmpty)
              //   Text(
              //     widget.caption,
              //     style: Theme.of(context)
              //         .textTheme
              //         .headlineSmall
              //         ?.copyWith(fontSize: 12.5.sp),
              //     maxLines: _isCaptionExpanded ? null : 2,
              //     overflow: _isCaptionExpanded
              //         ? TextOverflow.visible
              //         : TextOverflow.ellipsis,
              //   ),

              if (!widget.isTextPost && widget.caption.isNotEmpty)
                _buildCaptionWithHashtags(
                    context, widget.caption.safeForDisplay, _isCaptionExpanded ? null : 2, captionStyle),

            // "See More / See Less" for caption
            if (!widget.isTextPost)
              if (_hasCaptionOverflow && widget.caption.isNotEmpty)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isCaptionExpanded = !_isCaptionExpanded;
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(top: 2.0),
                    child: Text(
                      _isCaptionExpanded ? 'See Less' : 'See More',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColor,
                          ),
                    ),
                  ),
                ),

            // Comment display
            if (widget.comment.isNotEmpty)
              widget.isTextPost
                  ? Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: InkWell(
                        onTap: widget.commentonTap,
                        child: Text(
                          widget.comment.safeForDisplay,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontSize: 10.5.sp,
                                fontWeight: FontWeight.w500,
                              ),
                          maxLines: _isCommentExpanded ? null : 2,
                          overflow: _isCommentExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
                        ),
                      ),
                    )
                  : !_hasCaptionOverflow && widget.comment.isNotEmpty
                      ? Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: InkWell(
                            onTap: widget.commentonTap,
                            child: Text(
                              widget.comment.safeForDisplay,
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                    fontSize: 10.5.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                              maxLines: _isCommentExpanded ? null : 2,
                              overflow: _isCommentExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
                            ),
                          ),
                        )
                      : SizedBox.shrink(),
            // "See More / See Less" for Comment
            if (!_hasCaptionOverflow && _hasCommentOverflow && widget.comment.isNotEmpty)
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isCommentExpanded = !_isCommentExpanded;
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.only(top: 2.0),
                  child: Text(
                    _isCommentExpanded ? 'See Less' : 'See More',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).primaryColor,
                        ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

Widget _buildCaptionWithHashtags(
  BuildContext context,
  String text,
  int? maxLines,
  TextStyle? defaultStyle,
) {
  return RichText(
    maxLines: maxLines,
    overflow: maxLines != null ? TextOverflow.ellipsis : TextOverflow.visible,
    text: _buildHashtagTextSpan(context, text, defaultStyle),
  );
}

TextSpan _buildHashtagTextSpan(BuildContext context, String text, TextStyle? defaultStyle) {
  final List<TextSpan> spans = [];
  final safeText = text.safeForDisplay;
  final RegExp exp = RegExp(r'#[^\s]+');
  final matches = exp.allMatches(safeText);

  int start = 0;

  for (final match in matches) {
    if (match.start > start) {
      spans.add(TextSpan(
        text: safeText.substring(start, match.start),
        style: defaultStyle,
      ));
    }

    final String hashtag = safeText.substring(match.start, match.end);

    spans.add(TextSpan(
      text: hashtag,
      style: defaultStyle?.copyWith(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
      recognizer: TapGestureRecognizer()
        ..onTap = () {
          Logger.lOG("Tapped hashtag: $hashtag");
        },
    ));

    start = match.end;
  }

  if (start < safeText.length) {
    spans.add(TextSpan(
      text: safeText.substring(start),
      style: defaultStyle,
    ));
  }

  return TextSpan(children: spans);
}

// TextSpan _buildHashtagTextSpan(String text, TextStyle? defaultStyle) {
//   final List<TextSpan> spans = [];
//   final RegExp exp = RegExp(r'#[\w]+');
//   final matches = exp.allMatches(text);

//   int start = 0;

//   for (final match in matches) {
//     if (match.start > start) {
//       spans.add(TextSpan(
//         text: text.substring(start, match.start),
//         style: defaultStyle,
//       ));
//     }

//     spans.add(TextSpan(
//       text: text.substring(match.start, match.end),
//       style: defaultStyle?.copyWith(color: Colors.blue),
//     ));

//     start = match.end;
//   }

//   if (start < text.length) {
//     spans.add(TextSpan(
//       text: text.substring(start),
//       style: defaultStyle,
//     ));
//   }

//   return TextSpan(children: spans);
// }
